import { createRouter, createWebHashHistory } from 'vue-router'
import ThinkingTrainer from './components/ThinkingTrainer.vue'
import AnalysisPage from './components/AnalysisPage.vue'
import DeductiveAnalysisPage from './components/DeductiveAnalysisPage.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: ThinkingTrainer
  },
  {
    path: '/analysis',
    name: 'Analysis',
    component: AnalysisPage
  },
  {
    path: '/deductive-analysis',
    name: 'DeductiveAnalysis',
    component: DeductiveAnalysisPage
  }
]

const router = createRouter({
  // 使用Hash模式避免部署时的404问题
  // 如果服务器支持SPA路由配置，可以改回 createWebHistory()
  history: createWebHashHistory(),
  routes
})

export default router